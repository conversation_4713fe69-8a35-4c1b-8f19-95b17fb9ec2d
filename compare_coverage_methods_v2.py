#!/usr/bin/env python3
"""
对比新旧覆盖率计算方法的脚本
展示三种计算方法的差异：
1. 旧方法：matched_duration / rtk_duration
2. 时间对齐方法：matched_duration / aligned_duration  
3. 有效范围方法：matched_duration / effective_duration
"""

import json
import os
from datetime import datetime
from pathlib import Path


def parse_timestamp(timestamp_str):
    """解析时间戳字符串"""
    try:
        if '+' in timestamp_str:
            timestamp_str = timestamp_str.split('+')[0]
        return datetime.fromisoformat(timestamp_str)
    except:
        return None


def analyze_diagnostic_file(file_path):
    """分析诊断文件，计算三种覆盖率"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        metadata = data.get('metadata', {})
        segments = data.get('segments', [])
        statistics = data.get('statistics', {})
        missing_gaps = data.get('anomalies', {}).get('missing_gaps', [])
        
        if not segments:
            return None
        
        # 基本信息
        rtk_duration = metadata.get('rtk_duration', 0)
        matched_duration = metadata.get('matched_duration', 0)
        
        # 感知轨迹时间范围
        perception_start = None
        perception_end = None
        
        for seg in segments:
            start_time = parse_timestamp(seg.get('start_time', ''))
            end_time = parse_timestamp(seg.get('end_time', ''))
            
            if start_time and end_time:
                if perception_start is None or start_time < perception_start:
                    perception_start = start_time
                if perception_end is None or end_time > perception_end:
                    perception_end = end_time
        
        if not perception_start or not perception_end:
            return None
        
        # 计算有效分析时间范围
        effective_duration = (perception_end - perception_start).total_seconds()
        
        # 估算RTK时间范围
        rtk_start = perception_start  # 近似
        rtk_end = datetime.fromtimestamp(rtk_start.timestamp() + rtk_duration)
        
        # 计算对齐时间范围
        aligned_start = min(rtk_start, perception_start)
        aligned_end = max(rtk_end, perception_end)
        aligned_duration = (aligned_end - aligned_start).total_seconds()
        
        # 三种覆盖率计算方法
        old_coverage = (matched_duration / rtk_duration * 100) if rtk_duration > 0 else 0
        aligned_coverage = (matched_duration / aligned_duration * 100) if aligned_duration > 0 else 0
        effective_coverage = (matched_duration / effective_duration * 100) if effective_duration > 0 else 0
        
        # 分析漏检类型
        head_missing = 0
        middle_missing = 0
        tail_missing = 0
        
        for gap in missing_gaps:
            gap_type = gap.get('type', '')
            duration = gap.get('duration', 0)
            if gap_type == 'head_missing':
                head_missing += duration
            elif gap_type == 'middle_missing':
                middle_missing += duration
            elif gap_type == 'tail_missing':
                tail_missing += duration
        
        return {
            'file': os.path.basename(file_path),
            'rtk_duration': rtk_duration,
            'matched_duration': matched_duration,
            'effective_duration': effective_duration,
            'aligned_duration': aligned_duration,
            'old_coverage': old_coverage,
            'aligned_coverage': aligned_coverage,
            'effective_coverage': effective_coverage,
            'current_coverage': statistics.get('coverage_rate', 0),  # 当前文件中的覆盖率
            'head_missing': head_missing,
            'middle_missing': middle_missing,
            'tail_missing': tail_missing,
            'segments_count': len(segments)
        }
        
    except Exception as e:
        print(f"分析文件 {file_path} 失败: {e}")
        return None


def main():
    """主函数"""
    print("=" * 100)
    print("覆盖率计算方法对比分析 - 三种方法对比")
    print("=" * 100)
    
    # 查找诊断文件
    diagnostic_files = []
    for root, dirs, files in os.walk('output'):
        for file in files:
            if file.endswith('_diagnostic.json'):
                diagnostic_files.append(os.path.join(root, file))
    
    if not diagnostic_files:
        print("未找到诊断文件")
        return
    
    print(f"找到 {len(diagnostic_files)} 个诊断文件")
    print()
    
    # 分析结果
    results = []
    for file_path in diagnostic_files:
        result = analyze_diagnostic_file(file_path)
        if result:
            results.append(result)
    
    if not results:
        print("没有有效的分析结果")
        return
    
    # 显示结果表格
    print(f"{'文件名':<45} {'旧方法':<8} {'对齐方法':<8} {'有效方法':<8} {'当前值':<8} {'状态'}")
    print("-" * 100)
    
    for result in results:
        status = ""
        if result['old_coverage'] > 100:
            status = "🔴 旧方法超100%"
        elif result['aligned_coverage'] > 100:
            status = "🟡 对齐方法超100%"
        elif result['effective_coverage'] > 100:
            status = "🟠 有效方法超100%"
        else:
            status = "✅ 所有方法正常"
        
        print(f"{result['file']:<45} {result['old_coverage']:<8.1f} {result['aligned_coverage']:<8.1f} "
              f"{result['effective_coverage']:<8.1f} {result['current_coverage']:<8.1f} {status}")
    
    # 统计分析
    print("-" * 100)
    old_over_100 = sum(1 for r in results if r['old_coverage'] > 100)
    aligned_over_100 = sum(1 for r in results if r['aligned_coverage'] > 100)
    effective_over_100 = sum(1 for r in results if r['effective_coverage'] > 100)
    
    print(f"统计摘要:")
    print(f"  总文件数: {len(results)}")
    print(f"  旧方法超过100%: {old_over_100} 个")
    print(f"  对齐方法超过100%: {aligned_over_100} 个")
    print(f"  有效方法超过100%: {effective_over_100} 个")
    
    # 详细分析示例
    print("\n" + "=" * 100)
    print("详细分析示例（选择一个有代表性的文件）")
    print("=" * 100)
    
    # 选择一个有头部和尾部漏检的文件进行详细分析
    example = None
    for result in results:
        if result['head_missing'] > 0 and result['tail_missing'] > 0:
            example = result
            break
    
    if example:
        print(f"\n示例文件: {example['file']}")
        print(f"RTK轨迹时长: {example['rtk_duration']:.1f}s")
        print(f"匹配轨迹时长: {example['matched_duration']:.1f}s")
        print(f"有效分析时间范围: {example['effective_duration']:.1f}s")
        print(f"对齐时间范围: {example['aligned_duration']:.1f}s")
        print()
        print(f"漏检分析:")
        print(f"  头部漏检: {example['head_missing']:.1f}s (不计入有效方法)")
        print(f"  中间漏检: {example['middle_missing']:.1f}s (影响所有方法)")
        print(f"  尾部漏检: {example['tail_missing']:.1f}s (不计入有效方法)")
        print()
        print(f"三种计算方法:")
        print(f"  旧方法: {example['matched_duration']:.1f}s / {example['rtk_duration']:.1f}s = {example['old_coverage']:.1f}%")
        print(f"  对齐方法: {example['matched_duration']:.1f}s / {example['aligned_duration']:.1f}s = {example['aligned_coverage']:.1f}%")
        print(f"  有效方法: {example['matched_duration']:.1f}s / {example['effective_duration']:.1f}s = {example['effective_coverage']:.1f}%")
        print(f"  当前使用: {example['current_coverage']:.1f}%")
    
    print("\n" + "=" * 100)
    print("三种方法的特点对比:")
    print("1. 旧方法 (matched_duration / rtk_duration):")
    print("   - 可能超过100%，当感知轨迹延伸到RTK范围外时")
    print("   - 不考虑感知轨迹的实际时间范围")
    print()
    print("2. 对齐方法 (matched_duration / aligned_duration):")
    print("   - 避免超过100%的问题")
    print("   - 基于RTK和感知轨迹的时间并集")
    print("   - 头部和尾部漏检会降低覆盖率")
    print()
    print("3. 有效方法 (matched_duration / effective_duration):")
    print("   - 只考虑感知轨迹的有效检测时间范围")
    print("   - 头部和尾部漏检不影响覆盖率")
    print("   - 更准确反映感知系统在工作时间内的性能")
    print("=" * 100)


if __name__ == "__main__":
    main()
