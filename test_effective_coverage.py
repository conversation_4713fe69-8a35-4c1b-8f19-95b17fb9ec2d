#!/usr/bin/env python3
"""
测试有效覆盖率计算逻辑
验证头部和尾部漏检不计入覆盖率计算的新方法
"""

import sys
import os
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.output_generator import OutputGenerator
from core.data_utils import RTKPoint
from core.simple_distance_matcher import TrajectorySegment
from core.config_loader import load_config


class MockPerceptionPoint:
    """模拟感知点"""
    def __init__(self, timestamp, lat, lon, id_val):
        self.timestamp = timestamp
        self.lat = lat
        self.lon = lon
        self.id = id_val


def create_test_scenario():
    """
    创建测试场景：
    RTK轨迹: 0-30秒 (30秒)
    感知轨迹: 5-25秒 (20秒)
    
    这样会有：
    - 头部漏检: 0-5秒 (5秒)
    - 尾部漏检: 25-30秒 (5秒)
    - 有效分析范围: 5-25秒 (20秒)
    """
    base_time = datetime(2025, 7, 31, 10, 0, 0)
    
    # 创建RTK轨迹点 (0秒到30秒)
    rtk_points = []
    for i in range(31):  # 31个点，0-30秒
        rtk_points.append(RTKPoint(
            timestamp=base_time + timedelta(seconds=i),
            lat=39.9 + i * 0.0001,
            lon=116.3 + i * 0.0001,
            speed=10.0,
            heading=90.0
        ))
    
    # 创建感知轨迹段 (5秒到25秒)
    perception_points = []
    for i in range(20):  # 20个点，5-25秒
        perception_points.append(MockPerceptionPoint(
            timestamp=base_time + timedelta(seconds=5 + i),
            lat=39.9 + (5 + i) * 0.0001,
            lon=116.3 + (5 + i) * 0.0001,
            id_val="test_id_001"
        ))
    
    # 创建轨迹段
    matched_chain = [TrajectorySegment(
        id="test_id_001",
        points=perception_points,
        start_time=base_time + timedelta(seconds=5),
        end_time=base_time + timedelta(seconds=25)
    )]
    
    return rtk_points, matched_chain


def create_gap_scenario():
    """
    创建有间隙的测试场景：
    RTK轨迹: 0-30秒 (30秒)
    感知轨迹段1: 5-10秒 (5秒)
    感知轨迹段2: 15-25秒 (10秒)
    
    这样会有：
    - 头部漏检: 0-5秒 (5秒) - 不算漏检
    - 中间漏检: 10-15秒 (5秒) - 算漏检
    - 尾部漏检: 25-30秒 (5秒) - 不算漏检
    - 有效分析范围: 5-25秒 (20秒)
    - 匹配时长: 5+10=15秒
    - 覆盖率: 15/20 = 75%
    """
    base_time = datetime(2025, 7, 31, 10, 0, 0)
    
    # 创建RTK轨迹点 (0秒到30秒)
    rtk_points = []
    for i in range(31):
        rtk_points.append(RTKPoint(
            timestamp=base_time + timedelta(seconds=i),
            lat=39.9 + i * 0.0001,
            lon=116.3 + i * 0.0001,
            speed=10.0,
            heading=90.0
        ))
    
    # 创建第一个感知轨迹段 (5秒到10秒)
    perception_points_1 = []
    for i in range(5):
        perception_points_1.append(MockPerceptionPoint(
            timestamp=base_time + timedelta(seconds=5 + i),
            lat=39.9 + (5 + i) * 0.0001,
            lon=116.3 + (5 + i) * 0.0001,
            id_val="test_id_001"
        ))
    
    # 创建第二个感知轨迹段 (15秒到25秒)
    perception_points_2 = []
    for i in range(10):
        perception_points_2.append(MockPerceptionPoint(
            timestamp=base_time + timedelta(seconds=15 + i),
            lat=39.9 + (15 + i) * 0.0001,
            lon=116.3 + (15 + i) * 0.0001,
            id_val="test_id_002"
        ))
    
    # 创建轨迹段
    matched_chain = [
        TrajectorySegment(
            id="test_id_001",
            points=perception_points_1,
            start_time=base_time + timedelta(seconds=5),
            end_time=base_time + timedelta(seconds=10)
        ),
        TrajectorySegment(
            id="test_id_002",
            points=perception_points_2,
            start_time=base_time + timedelta(seconds=15),
            end_time=base_time + timedelta(seconds=25)
        )
    ]
    
    return rtk_points, matched_chain


def test_coverage_calculation():
    """测试覆盖率计算"""
    print("=" * 80)
    print("有效覆盖率计算测试")
    print("=" * 80)
    
    # 加载配置
    config = load_config()
    output_generator = OutputGenerator(config)
    
    # 测试场景1：连续轨迹
    print("\n测试场景1：连续轨迹（无中间间隙）")
    print("-" * 50)
    rtk_points, matched_chain = create_test_scenario()
    
    print(f"RTK轨迹时间范围: {rtk_points[0].timestamp} ~ {rtk_points[-1].timestamp}")
    print(f"RTK轨迹时长: {(rtk_points[-1].timestamp - rtk_points[0].timestamp).total_seconds()}s")
    
    perception_start = min(seg.start_time for seg in matched_chain)
    perception_end = max(seg.end_time for seg in matched_chain)
    print(f"感知轨迹时间范围: {perception_start} ~ {perception_end}")
    print(f"有效分析时间范围: {(perception_end - perception_start).total_seconds()}s")
    
    matched_duration = sum(seg.duration for seg in matched_chain)
    print(f"匹配时长: {matched_duration}s")
    
    coverage_rate = output_generator._calculate_coverage_rate(matched_chain, rtk_points)
    print(f"覆盖率: {coverage_rate}%")
    
    expected_coverage = (matched_duration / (perception_end - perception_start).total_seconds()) * 100
    print(f"期望覆盖率: {expected_coverage:.2f}%")
    print(f"计算正确: {'✅' if abs(coverage_rate - expected_coverage) < 0.01 else '❌'}")
    
    # 测试场景2：有间隙的轨迹
    print("\n测试场景2：有间隙的轨迹")
    print("-" * 50)
    rtk_points, matched_chain = create_gap_scenario()
    
    print(f"RTK轨迹时间范围: {rtk_points[0].timestamp} ~ {rtk_points[-1].timestamp}")
    print(f"RTK轨迹时长: {(rtk_points[-1].timestamp - rtk_points[0].timestamp).total_seconds()}s")
    
    perception_start = min(seg.start_time for seg in matched_chain)
    perception_end = max(seg.end_time for seg in matched_chain)
    print(f"感知轨迹时间范围: {perception_start} ~ {perception_end}")
    print(f"有效分析时间范围: {(perception_end - perception_start).total_seconds()}s")
    
    matched_duration = sum(seg.duration for seg in matched_chain)
    print(f"匹配时长: {matched_duration}s")
    
    coverage_rate = output_generator._calculate_coverage_rate(matched_chain, rtk_points)
    print(f"覆盖率: {coverage_rate}%")
    
    expected_coverage = (matched_duration / (perception_end - perception_start).total_seconds()) * 100
    print(f"期望覆盖率: {expected_coverage:.2f}%")
    print(f"计算正确: {'✅' if abs(coverage_rate - expected_coverage) < 0.01 else '❌'}")
    
    # 分析间隙
    print(f"\n间隙分析:")
    print(f"  头部漏检: {rtk_points[0].timestamp} ~ {perception_start} ({(perception_start - rtk_points[0].timestamp).total_seconds()}s) - 不计入覆盖率")
    print(f"  中间漏检: {matched_chain[0].end_time} ~ {matched_chain[1].start_time} ({(matched_chain[1].start_time - matched_chain[0].end_time).total_seconds()}s) - 影响覆盖率")
    print(f"  尾部漏检: {perception_end} ~ {rtk_points[-1].timestamp} ({(rtk_points[-1].timestamp - perception_end).total_seconds()}s) - 不计入覆盖率")
    
    print("\n" + "=" * 80)
    print("新覆盖率计算方法特点:")
    print("1. 只考虑感知轨迹时间范围内的覆盖情况")
    print("2. 头部和尾部漏检不影响覆盖率计算")
    print("3. 覆盖率反映感知系统在有效检测时间内的性能")
    print("4. 更准确地评估感知系统的连续性和稳定性")
    print("=" * 80)


if __name__ == "__main__":
    test_coverage_calculation()
